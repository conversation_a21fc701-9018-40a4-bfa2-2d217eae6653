import 'package:flutter/material.dart';
import 'package:face_liveness_detector/face_liveness_detector.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:amplify_flutter/amplify_flutter.dart';
import 'package:amplify_api/amplify_api.dart';
import 'package:amplify_auth_cognito/amplify_auth_cognito.dart';
import 'amplifyconfiguration.dart';
import 'package:flutter/services.dart';

void main() async {
  // Đảm bảo Flutter widgets binding được khởi tạo
  WidgetsFlutterBinding.ensureInitialized();
  
  // Khởi tạo Amplify
  await _configureAmplify();
  
  runApp(const MyApp());
}

Future<void> _configureAmplify() async {
  try {
    // Thêm cả Auth và API plugins để hỗ trợ IAM authentication
    final auth = AmplifyAuthCognito();
    final api = AmplifyAPI();
    
    await Amplify.addPlugin(auth);
    await Amplify.addPlugin(api);
    
    // Cấu hình Amplify
    await Amplify.configure(amplifyconfig);
    
    safePrint('✅ Amplify configured successfully with Auth + API');
  } catch (e) {
    safePrint('❌ Error configuring Amplify: $e');
  }
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Face Liveness với STS Credentials',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
        useMaterial3: true,
      ),
      home: const FaceLivenessSTSPage(),
    );
  }
}

class FaceLivenessSTSPage extends StatefulWidget {
  const FaceLivenessSTSPage({super.key});

  @override
  State<FaceLivenessSTSPage> createState() => _FaceLivenessSTSPageState();
}

class _FaceLivenessSTSPageState extends State<FaceLivenessSTSPage> {
  String _resultMessage = 'Chưa có kết quả test';
  bool _isLoading = false;
  String? _auditImageBase64;
  bool _isLive = false;
  double _confidenceScore = 0.0;
  
  // STS Credentials
  String? _accessKeyId;
  String? _secretAccessKey;
  String? _sessionToken;
  String? _awsRegion;
  DateTime? _credentialsExpiration;

  @override
  void initState() {
    super.initState();
    setState(() {
      _resultMessage = '🔍 Đang kiểm tra và request quyền camera...';
    });
    // Request camera permission ngay khi vào app với delay để đảm bảo UI đã render
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _requestCameraPermissionOnStart();
    });
  }

  Future<void> _requestCameraPermissionOnStart() async {
    try {
      print('🚀 App started - requesting camera permission immediately...');

      // Delay để đảm bảo UI đã render hoàn toàn
      await Future.delayed(const Duration(milliseconds: 1000));

      final hasPermission = await _requestCameraPermission();

      if (hasPermission) {
        setState(() {
          _resultMessage = '✅ Quyền camera đã được cấp!\n\n'
              'Sẵn sàng test Face Liveness với STS credentials!';
        });
      } else {
        setState(() {
          _resultMessage = '❌ Không có quyền camera.\n\n'
              'Vui lòng cấp quyền camera để sử dụng Face Liveness.\n\n'
              'Có thể thử:\n'
              '• Tap "Request Camera Permission" để thử lại\n'
              '• Tap "Test Direct Face Liveness" để bypass permission_handler\n'
              '• Vào Settings manually để cấp quyền\n'
              '• Restart app sau khi cấp quyền';
        });
      }
    } catch (e) {
      print('💥 Error in _requestCameraPermissionOnStart: $e');
      setState(() {
        _resultMessage = '❌ Lỗi khi request quyền camera: $e\n\n'
            'Thử các cách khác:\n'
            '• Tap "Request Camera Permission" để thử lại\n'
            '• Tap "Test Direct Face Liveness"\n'
            '• Restart app\n'
            '• Check iOS Settings manually';
      });
    }
  }

  Future<bool> _requestCameraPermission() async {
    try {
      print('🔍 Checking camera permission status...');

      // Trên iOS, đôi khi cần delay để permission system sẵn sàng
      if (Theme.of(context).platform == TargetPlatform.iOS) {
        await Future.delayed(const Duration(milliseconds: 500));
      }

      final status = await Permission.camera.status;
      print('📱 Current camera permission status: $status');

      if (status.isGranted) {
        print('✅ Camera permission already granted');
        return true;
      }

      if (status.isPermanentlyDenied) {
        print('❌ Camera permission permanently denied - opening settings');
        if (mounted) {
          setState(() {
            _resultMessage = '❌ Quyền camera bị từ chối vĩnh viễn.\n\n'
                'Vui lòng vào Settings > Privacy & Security > Camera để cấp quyền cho app này.';
            _isLoading = false;
          });

          final shouldOpenSettings = await _showPermissionDialog();
          if (shouldOpenSettings) {
            await openAppSettings();
          }
        }
        return false;
      }

      // Request permission với dialog rõ ràng
      if (mounted) {
        setState(() {
          _resultMessage = 'Đang yêu cầu quyền truy cập camera...\n\n'
              'Vui lòng chọn "Allow" trong dialog sắp xuất hiện.';
        });
      }

      print('🙏 Requesting camera permission...');

      // Trên iOS, đôi khi cần thêm delay trước khi request
      if (Theme.of(context).platform == TargetPlatform.iOS) {
        await Future.delayed(const Duration(milliseconds: 300));
      }

      final result = await Permission.camera.request();
      print('📋 Permission request result: $result');

      if (result.isGranted) {
        print('✅ Camera permission granted successfully');
        if (mounted) {
          setState(() {
            _resultMessage = '✅ Quyền camera đã được cấp!';
          });
        }
        return true;
      } else if (result.isPermanentlyDenied) {
        print('❌ Camera permission permanently denied after request');
        if (mounted) {
          setState(() {
            _resultMessage = '❌ Quyền camera bị từ chối vĩnh viễn.\n\n'
                'Vui lòng vào Settings > Privacy & Security > Camera để cấp quyền cho app này.';
            _isLoading = false;
          });

          final shouldOpenSettings = await _showPermissionDialog();
          if (shouldOpenSettings) {
            await openAppSettings();
          }
        }
        return false;
      } else {
        print('❌ Camera permission denied');
        if (mounted) {
          setState(() {
            _resultMessage = '❌ Quyền camera bị từ chối.\n\n'
                'Cần quyền camera để sử dụng Face Liveness.\n'
                'Vui lòng thử lại và chọn "Allow" khi được hỏi.';
            _isLoading = false;
          });
        }
        return false;
      }

    } catch (e) {
      print('💥 Error requesting camera permission: $e');
      if (mounted) {
        setState(() {
          _resultMessage = '❌ Lỗi khi yêu cầu quyền camera: $e\n\n'
              'Có thể do:\n'
              '• iOS version không tương thích\n'
              '• Permission handler plugin lỗi\n'
              '• App không có permission trong Info.plist\n\n'
              'Thử restart app hoặc cấp quyền manual trong Settings.';
          _isLoading = false;
        });
      }
      return false;
    }
  }

  Future<bool> _showPermissionDialog() async {
    return await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Cần quyền Camera'),
          content: const Text(
            'App cần quyền truy cập camera để thực hiện Face Liveness detection.\n\n'
            'Vui lòng vào Settings > Privacy & Security > Camera để cấp quyền cho app này.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Hủy'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('Mở Settings'),
            ),
          ],
        );
      },
    ) ?? false;
  }

  Future<void> _testDirectFaceLiveness() async {
    setState(() {
      _isLoading = true;
      _resultMessage = 'Đang thử direct Face Liveness - bypass permission_handler...';
    });

    try {
      // Lấy sessionID từ Amplify API trước
      setState(() {
        _resultMessage = 'Đang lấy sessionID từ Amplify API...';
      });
      
      final sessionId = await _getSessionIdFromAmplify();
      if (sessionId == null) {
        setState(() {
          _isLoading = false;
        });
        return;
      }

      // Lấy STS credentials
      final hasCredentials = await _getSTSCredentials();
      if (!hasCredentials) {
        setState(() {
          _isLoading = false;
        });
        return;
      }
      
      setState(() {
        _resultMessage = 'Đang khởi chạy Face Liveness trực tiếp (không qua permission_handler)...';
      });

      // Gọi trực tiếp Face Liveness plugin
      await _runFaceLivenessWithSTSCredentials(sessionId);

    } catch (e) {
      print('💥 Error with direct Face Liveness: $e');
      setState(() {
        _resultMessage = '❌ Lỗi với direct Face Liveness: $e\n\n'
            'Có thể do:\n'
            '• iOS vẫn cần permission được grant trước\n'
            '• Plugin yêu cầu permission_handler\n'
            '• Camera access bị block ở level OS';
        _isLoading = false;
      });
    }
  }

  Future<String?> _getSessionIdFromAmplify() async {
    try {
      setState(() {
        _resultMessage = 'Đang lấy sessionID từ Amplify API...';
      });

      print('🔄 Starting Amplify API call...');
      print('📡 API Name: livenessBackendAPI');
      print('🛣️ Path: /liveness/create');

      // Sử dụng Amplify API để gọi REST endpoint
      final restOperation = Amplify.API.post(
        '/liveness/create',
        // apiName: 'livenessBackendAPI', // Tên API từ amplifyconfiguration.dart
      );

      print('⏳ Waiting for response...');
      final response = await restOperation.response;
      print('📥 Response received - Status: ${response.statusCode}');
      
      final responseBody = response.decodeBody();
      print('📄 Response body: $responseBody');
      
      if (response.statusCode == 200) {
        final responseData = jsonDecode(responseBody);
        
        if (responseData['sessionId'] != null) {
          setState(() {
            _resultMessage = '✅ SessionID đã lấy thành công từ Amplify!\n\n'
                '🆔 Session ID: ${responseData['sessionId']}\n'
                '📡 Từ: AWS Rekognition via Amplify API Client\n'
                '🚀 Sử dụng: amplify_api package\n\n'
                'Sẵn sàng test Face Liveness!';
          });
          
          return responseData['sessionId'];
        } else {
          setState(() {
            _resultMessage = '❌ Không có sessionId trong response: $responseBody';
          });
          return null;
        }
      } else {
        setState(() {
          _resultMessage = '❌ API Error ${response.statusCode}: $responseBody';
        });
        return null;
      }
    } on ApiException catch (e) {
      print('🚨 ApiException caught:');
      print('   Message: ${e.message}');
      print('   Recovery: ${e.recoverySuggestion}');
      print('   Underlying: ${e.underlyingException}');
      
      setState(() {
        _resultMessage = '❌ Amplify API Exception: ${e.message}\n\n'
            'Details: ${e.recoverySuggestion}\n\n'
            'Underlying: ${e.underlyingException}\n\n'
            'Có thể do:\n'
            '• Lambda function createSession lỗi\n'
            '• IAM permissions thiếu\n'
            '• Rekognition service không khả dụng';
      });
      return null;
    } catch (e) {
      print('💥 General Exception caught: $e');
      print('   Type: ${e.runtimeType}');
      
      setState(() {
        _resultMessage = '❌ Lỗi không xác định với Amplify API: $e\n\n'
            'Type: ${e.runtimeType}\n\n'
            'Hãy đảm bảo:\n'
            '• Amplify đã được configure đúng\n'
            '• API đã được deploy\n'
            '• Lambda function createSession hoạt động\n'
            '• Network connection ổn định';
      });
      return null;
    }
  }

  Future<bool> _getSTSCredentials() async {
    try {
      setState(() {
        _resultMessage = 'Đang lấy STS credentials từ server...';
      });

      const stsServerUrl = 'http://localhost:3000/api/credentials';
      
      final response = await http.post(
        Uri.parse(stsServerUrl),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'sessionName': 'face-liveness-${DateTime.now().millisecondsSinceEpoch}',
          'duration': 3600, // 1 hour
        }),
      ).timeout(
        const Duration(seconds: 15),
        onTimeout: () => throw Exception('STS server timeout after 15 seconds'),
      );

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        
        if (responseData['success'] == true) {
          final credentials = responseData['credentials'];
          
          setState(() {
            _accessKeyId = credentials['accessKeyId'];
            _secretAccessKey = credentials['secretAccessKey'];
            _sessionToken = credentials['sessionToken'];
            _awsRegion = responseData['region'];
            _credentialsExpiration = DateTime.parse(credentials['expiration']);
            
            _resultMessage = '✅ STS Credentials đã lấy thành công!\n\n'
                '🔑 Access Key: ${_accessKeyId?.substring(0, 8)}...\n'
                '🌍 Region: $_awsRegion\n'
                '⏰ Hết hạn: ${_credentialsExpiration?.toString().substring(0, 19)}\n\n'
                'Sẵn sàng test Face Liveness!';
          });
          
          return true;
        } else {
          setState(() {
            _resultMessage = '❌ Lỗi từ STS server: ${responseData['error']}';
          });
          return false;
        }
      } else {
        setState(() {
          _resultMessage = '❌ HTTP Error ${response.statusCode}: ${response.body}';
        });
        return false;
      }
    } catch (e) {
      setState(() {
        _resultMessage = '❌ Lỗi kết nối STS server: $e\n\n'
            'Hãy đảm bảo:\n'
            '• STS server đang chạy trên localhost:3000\n'
            '• ADB reverse port forwarding đã setup\n'
            '• AWS credentials được cấu hình đúng\n'
            '• ROLE_ARN đã được set';
      });
      return false;
    }
  }

  void _testFaceLivenessWithSTS() async {
    setState(() {
      _isLoading = true;
      _resultMessage = 'Đang khởi tạo test với STS credentials...';
      // Reset previous results
      _auditImageBase64 = null;
      _isLive = false;
      _confidenceScore = 0.0;
    });

    try {
      // Bước 1: Kiểm tra quyền camera một lần nữa
      setState(() {
        _resultMessage = 'Đang kiểm tra quyền camera...';
      });
      
      final cameraStatus = await Permission.camera.status;
      if (!cameraStatus.isGranted) {
        setState(() {
          _resultMessage = '❌ Quyền camera chưa được cấp.\n\n'
              'Vui lòng:\n'
              '• Cấp quyền camera trong Settings\n'
              '• Hoặc tap "Test Direct Face Liveness"\n'
              '• Restart app sau khi cấp quyền';
          _isLoading = false;
        });
        return;
      }

      // Bước 2: Lấy sessionID từ Amplify API (AWS Rekognition)
      final sessionId = await _getSessionIdFromAmplify();
      if (sessionId == null) {
        setState(() {
          _isLoading = false;
        });
        return;
      }

      // Bước 3: Lấy STS credentials từ server
      final hasCredentials = await _getSTSCredentials();
      if (!hasCredentials) {
        setState(() {
          _isLoading = false;
        });
        return;
      }
      
      setState(() {
        _resultMessage = 'SessionID: $sessionId\nĐang khởi chạy Face Liveness với Amplify sessionID + STS credentials...';
      });

      // Bước 4: Test plugin Face Liveness Detector với Amplify sessionID và STS credentials
      await _runFaceLivenessWithSTSCredentials(sessionId);

    } catch (e) {
      setState(() {
        _resultMessage = 'Lỗi test với STS: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _runFaceLivenessWithSTSCredentials(String sessionId) async {
    try {
      setState(() {
        _resultMessage = 'Amplify SessionID: $sessionId\nĐang khởi chạy camera với Amplify sessionID + STS credentials...';
      });

      // Test plugin Face Liveness Detector với STS credentials
      final result = await Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => Scaffold(
            appBar: AppBar(
              title: const Text('🔐 Face Liveness với Amplify + STS'),
              backgroundColor: Colors.indigo,
              foregroundColor: Colors.white,
            ),
            body: SizedBox(
              width: double.infinity,
              height: double.infinity,
              child: FaceLivenessDetector(
                sessionId: sessionId,
                region: _awsRegion ?? 'ap-northeast-1',
                // Truyền custom credentials vào plugin
                accessKeyId: _accessKeyId,
                secretAccessKey: _secretAccessKey,
                sessionToken: _sessionToken,
                onComplete: () {
                  print('✅ STS Face Liveness completed successfully');
                  Navigator.pop(context, {'success': true, 'isLive': true});
                },
                onError: (String error) {
                  print('❌ STS Face Liveness error: $error');
                  Navigator.pop(context, {'success': false, 'error': error});
                },
              ),
            ),
          ),
        ),
      );
      
      if (result != null && result['success'] == true) {
        // Amplify + STS Face Liveness test thành công
        setState(() {
          _resultMessage = 'Amplify SessionID: $sessionId\n'
              '✅ Face Liveness với Amplify + STS thành công!\n\n'
              '🎯 Kết quả test:\n'
              '• Amplify API sessionID ✅\n'
              '• STS credentials hoạt động ✅\n'
              '• Plugin tải với custom credentials ✅\n'
              '• Camera permissions OK ✅\n'
              '• UI hiển thị đúng ✅\n'
              '• AWS connection established ✅\n\n'
              '🎉 Tích hợp Amplify + STS + Face Liveness thành công!';
          
          // Fake một số data để test UI
          _isLive = true;
          _confidenceScore = 92.5;
          _auditImageBase64 = _generateFakeImageBase64();
          _isLoading = false;
        });
        
      } else {
        final error = result?['error'] ?? 'Không rõ lỗi';
        setState(() {
          _resultMessage = 'Amplify SessionID: $sessionId\n'
              '❌ Face Liveness với Amplify + STS thất bại: $error\n\n'
              'Có thể do:\n'
              '• Amplify sessionID không hợp lệ\n'
              '• STS credentials không đúng\n'
              '• AWS permissions thiếu\n'
              '• Network connection\n'
              '• Region mismatch\n'
              '• Plugin configuration';
          _isLoading = false;
        });
      }
      
    } catch (error) {
      setState(() {
        _resultMessage = 'Amplify SessionID: $sessionId\n'
            '❌ Exception với Amplify + STS: $error\n\n'
            'Có thể do:\n'
            '• Amplify sessionID expired\n'
            '• STS credentials expired\n'
            '• Plugin không nhận được credentials\n'
            '• AWS service unavailable\n'
            '• Network/Platform channel issues';
        _isLoading = false;
      });
    }
  }

  String _generateFakeImageBase64() {
    // Tạo fake base64 image data để test UI
    const fakeImageBytes = [
      137, 80, 78, 71, 13, 10, 26, 10, 0, 0, 0, 13, 73, 72, 68, 82, 0, 0, 0, 1, 0, 0, 0, 1, 8, 2, 0, 0, 0, 144, 119, 83, 222, 0, 0, 0, 12, 73, 68, 65, 84, 8, 215, 99, 248, 15, 0, 0, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0
    ];
    return base64Encode(fakeImageBytes);
  }

  Widget _buildSTSInfoSection() {
    if (_accessKeyId == null) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.only(top: 20),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.indigo[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.indigo[300]!,
          width: 2,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.security,
                color: Colors.indigo[700],
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'STS Credentials Information',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                  color: Colors.indigo[700],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.indigo[100],
              borderRadius: BorderRadius.circular(6),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '🔑 Access Key: ${_accessKeyId?.substring(0, 12)}...\n'
                  '🌍 Region: $_awsRegion\n'
                  '⏰ Expires: ${_credentialsExpiration?.toString().substring(0, 19)}\n'
                  '🎯 Status: Active & Ready',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.indigo[800],
                    fontFamily: 'monospace',
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTestResultSection() {
    if (_auditImageBase64 == null || _auditImageBase64!.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.only(top: 20),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _isLive ? Colors.green[50] : Colors.red[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _isLive ? Colors.green[300]! : Colors.red[300]!,
          width: 2,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.verified_user,
                color: _isLive ? Colors.green[700] : Colors.red[700],
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'Amplify + STS + Face Liveness Results',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                  color: _isLive ? Colors.green[700] : Colors.red[700],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: _isLive ? Colors.green[100] : Colors.red[100],
              borderRadius: BorderRadius.circular(6),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.analytics,
                      color: _isLive ? Colors.green[800] : Colors.red[800],
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'Confidence Score: ${_confidenceScore.toStringAsFixed(2)}%',
                        style: TextStyle(
                          fontWeight: FontWeight.w600,
                          color: _isLive ? Colors.green[800] : Colors.red[800],
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  '• Amplify API sessionID ✅\n'
                  '• STS server connection ✅\n'
                  '• AWS temporary credentials ✅\n'
                  '• Plugin custom credentials ✅\n'
                  '• Face Liveness detection ✅\n'
                  '• Complete integration success ✅',
                  style: TextStyle(
                    fontSize: 12,
                    color: _isLive ? Colors.green[800] : Colors.red[800],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        title: const Text('🔐 Face Liveness + Amplify + STS'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: SingleChildScrollView(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              const Icon(
                Icons.security,
                size: 80,
                color: Colors.indigo,
              ),
              const SizedBox(height: 20),
              const Text(
                'Face Liveness với Amplify + STS',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 10),
              const Text(
                'Tích hợp Amplify API Client và STS credentials với Face Liveness plugin.\n\nLuồng test:\n• Dùng amplify_api để gọi Lambda createSession\n• Lấy SessionID từ AWS Rekognition\n• Lấy STS credentials từ server\n• Truyền cả hai vào plugin\n• Chạy Face Liveness detection\n• Hiển thị kết quả hoàn chỉnh',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 16),
              ),
              const SizedBox(height: 40),
              if (_isLoading)
                const CircularProgressIndicator()
              else
                Column(
                  children: [
                    ElevatedButton.icon(
                      onPressed: _testFaceLivenessWithSTS,
                      icon: const Icon(Icons.play_arrow),
                      label: Text(
                        _auditImageBase64 != null ? 'Test lại với Amplify + STS' : 'Bắt đầu Test Amplify + STS + Face Liveness',
                        style: const TextStyle(fontSize: 16),
                      ),
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 32,
                          vertical: 16,
                        ),
                        backgroundColor: Colors.indigo,
                        foregroundColor: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 12),
                    ElevatedButton.icon(
                      onPressed: () async {
                        setState(() {
                          _isLoading = true;
                          _resultMessage = 'Đang request quyền camera...';
                        });

                        final hasPermission = await _requestCameraPermission();

                        setState(() {
                          _isLoading = false;
                          if (hasPermission) {
                            _resultMessage = '✅ Quyền camera đã được cấp thành công!\n\n'
                                'Bây giờ bạn có thể sử dụng Face Liveness.';
                          } else {
                            _resultMessage = '❌ Quyền camera vẫn chưa được cấp.\n\n'
                                'Vui lòng kiểm tra Settings hoặc thử lại.';
                          }
                        });
                      },
                      icon: const Icon(Icons.security),
                      label: const Text(
                        'Request Camera Permission',
                        style: TextStyle(fontSize: 14),
                      ),
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 24,
                          vertical: 12,
                        ),
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 12),
                    ElevatedButton.icon(
                      onPressed: _testDirectFaceLiveness,
                      icon: const Icon(Icons.camera_alt),
                      label: const Text(
                        'Test Direct Face Liveness (Bypass Permission)',
                        style: TextStyle(fontSize: 14),
                      ),
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 24,
                          vertical: 12,
                        ),
                        backgroundColor: Colors.orange,
                        foregroundColor: Colors.white,
                      ),
                    ),
                    if (_auditImageBase64 != null) ...[
                      const SizedBox(height: 12),
                      OutlinedButton.icon(
                        onPressed: () {
                          setState(() {
                            _auditImageBase64 = null;
                            _isLive = false;
                            _confidenceScore = 0.0;
                            _accessKeyId = null;
                            _secretAccessKey = null;
                            _sessionToken = null;
                            _awsRegion = null;
                            _credentialsExpiration = null;
                            _resultMessage = 'Đã xóa kết quả test. Sẵn sàng cho test mới.';
                          });
                        },
                        icon: const Icon(Icons.clear, size: 18),
                        label: const Text('Clear All Results'),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: Colors.grey[600],
                        ),
                      ),
                    ],
                  ],
                ),
              const SizedBox(height: 30),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Test Results:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _resultMessage,
                      style: const TextStyle(fontSize: 14),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 20),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.indigo[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.indigo[300]!),
                ),
                child: const Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '🔐 Amplify + STS Integration Flow:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                        color: Colors.indigo,
                      ),
                    ),
                    SizedBox(height: 4),
                    Text(
                      '1. amplify_api.post() → Lambda createSession\n2. AWS Rekognition → Generate SessionID\n3. Call STS server → Get temporary credentials\n4. Pass both to plugin → Custom provider\n5. Face Liveness → AWS Rekognition\n6. Display results → Complete integration',
                      style: TextStyle(fontSize: 12, color: Colors.indigo),
                    ),
                  ],
                ),
              ),
              // Hiển thị STS credentials info
              _buildSTSInfoSection(),
              // Hiển thị test results section
              _buildTestResultSection(),
            ],
          ),
        ),
      ),
    );
  }
}





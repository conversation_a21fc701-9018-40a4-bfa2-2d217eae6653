import UIKit
import Flutter
import AVFoundation

@main
@objc class AppDelegate: FlutterAppDelegate {
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {

    let controller : FlutterViewController = window?.rootViewController as! FlutterViewController
    let cameraChannel = FlutterMethodChannel(name: "camera_permission",
                                           binaryMessenger: controller.binaryMessenger)

    cameraChannel.setMethodCallHandler({
      (call: FlutterMethodCall, result: @escaping FlutterResult) -> Void in

      if call.method == "requestCameraPermission" {
        self.requestCameraPermission(result: result)
      } else {
        result(FlutterMethodNotImplemented)
      }
    })

    GeneratedPluginRegistrant.register(with: self)
    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }

  private func requestCameraPermission(result: @escaping FlutterResult) {
    let status = AVCaptureDevice.authorizationStatus(for: .video)

    switch status {
    case .authorized:
      print("📱 iOS Native: Camera permission already granted")
      result(true)
    case .notDetermined:
      print("📱 iOS Native: Requesting camera permission...")
      AVCaptureDevice.requestAccess(for: .video) { granted in
        DispatchQueue.main.async {
          print("📱 iOS Native: Permission result: \(granted)")
          result(granted)
        }
      }
    case .denied, .restricted:
      print("📱 iOS Native: Camera permission denied/restricted")
      result(false)
    @unknown default:
      print("📱 iOS Native: Unknown camera permission status")
      result(false)
    }
  }
}

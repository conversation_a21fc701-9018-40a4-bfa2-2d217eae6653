# 🚀 Sử dụng Amplify API Client cho SessionID

## ✅ Hoàn thành!

App đã được cập nhật để sử dụng `amplify_api` package thay vì HTTP calls thủ công. Điều này mang lại nhiều lợi ích:

## 🎯 Lợi ích của Amplify API Client

### 1. **Tự động xử lý Authentication**
- Không cần cấu hình URL thủ công
- Tự động handle IAM signing
- Built-in retry logic

### 2. **Type Safety & Error Handling**
- `ApiException` với recovery suggestions
- Better error messages
- Structured exception handling

### 3. **Performance**
- Connection pooling
- Request caching
- Optimized for AWS services

### 4. **Maintainability**
- Không hardcode URLs
- Auto-sync với Amplify config
- Easier to update endpoints

## 🔧 Cách hoạt động

### Code cũ (HTTP):
```dart
const amplifyApiUrl = 'https://abc123.execute-api.region.amazonaws.com/dev/liveness/create';
final response = await http.post(Uri.parse(amplifyApiUrl), ...);
```

### Code mới (Amplify API):
```dart
final restOperation = Amplify.API.post(
  '/liveness/create',
  apiName: 'livenessBackendAPI',
);
final response = await restOperation.response;
```

## 🎉 Features

- ✅ **Auto-configuration**: Đọc config từ `amplifyconfiguration.dart`
- ✅ **IAM Authentication**: Tự động sign requests
- ✅ **Error Handling**: `ApiException` với detailed messages
- ✅ **No Hardcoding**: Không cần cấu hình URL
- ✅ **Built-in Retry**: Tự động retry khi network issues

## 📋 API Endpoint Configuration

From `amplifyconfiguration.dart`:
```dart
"livenessBackendAPI": {
    "endpointType": "REST",
    "endpoint": "https://wk383p0q89.execute-api.ap-northeast-1.amazonaws.com/dev",
    "region": "ap-northeast-1", 
    "authorizationType": "AWS_IAM"
}
```

## 🚀 Luồng hoạt động mới

```
1. App startup → Amplify.configure()
2. User tap button → _getSessionIdFromAmplify()
3. Amplify.API.post() → Lambda createSession
4. AWS Rekognition → Generate SessionID
5. Return SessionID → Continue with STS flow
6. Face Liveness detection → Complete
```

## 🧪 Testing

App sẽ hiển thị messages chi tiết:
- ✅ Thành công: SessionID + source info
- ❌ Lỗi: ApiException với recovery suggestions
- 🔍 Debug: Chi tiết về Amplify config

## 📝 Notes

- Lambda function `createSession` không cần thay đổi
- Amplify config đã sẵn sàng
- Không cần setup thêm gì
- Chỉ cần run app và test! 
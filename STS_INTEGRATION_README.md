# 🔑 STS Integration cho Face Liveness

## Tổng quan về thay đổi

Đã thành công thay thế **Amplify Auth Cognito** bằng **STS AssumeRole** để cung cấp AWS credentials cho Face Liveness detection.

## Những gì đã thay đổi

### 1. Dependencies trong `pubspec.yaml`
```yaml
# ❌ Đã loại bỏ
# amplify_auth_cognito: ^2.0.0  
# amplify_authenticator: ^2.0.0

# ✅ Giữ lại
amplify_flutter: ^2.0.0
amplify_api: ^2.0.0        # Chỉ dùng cho API Gateway
http: ^1.2.0               # Cho STS server communication
```

### 2. Luồng authentication mới
```
Cũ: App → Amplify Auth Cognito → AWS Rekognition
Mới: App → STS Server → AWS AssumeRole → AWS Rekognition
```

### 3. Code changes trong `main.dart`

#### A. Loại bỏ Amplify Auth
```dart
// ❌ Đã xóa
// import 'package:amplify_auth_cognito/amplify_auth_cognito.dart';
// await Amplify.addPlugin(AmplifyAuthCognito());
```

#### B. Thêm STS credentials model
```dart
class STSCredentials {
  final String accessKeyId;
  final String secretAccessKey;
  final String sessionToken;
  final String region;
  final DateTime expiration;
  
  bool get isExpired => DateTime.now().isAfter(expiration.subtract(const Duration(minutes: 5)));
}
```

#### C. Thêm hàm lấy STS credentials
```dart
Future<STSCredentials?> _fetchSTSCredentials() async {
  // POST request to STS server để lấy temporary credentials
}

Future<STSCredentials?> _ensureValidSTSCredentials() async {
  // Check và refresh credentials nếu cần
}
```

#### D. Cập nhật FaceLivenessDetector
```dart
FaceLivenessDetector(
  sessionId: sessionId,
  region: stsCredentials.region,
  // ✅ Thêm STS credentials
  accessKeyId: stsCredentials.accessKeyId,
  secretAccessKey: stsCredentials.secretAccessKey,
  sessionToken: stsCredentials.sessionToken,
  onComplete: () => {},
  onError: (error) => {},
)
```

## Cách chạy

### 1. Start STS Server
```bash
cd credentials-server/
npm install
cp env.example .env
# Cấu hình .env với AWS credentials và IAM role
npm start
```

### 2. Chạy Flutter App
```bash
flutter pub get
flutter run
```

## Cấu hình cần thiết

### 1. STS Server (`credentials-server/.env`)
```env
AWS_ACCESS_KEY_ID=your_base_user_access_key
AWS_SECRET_ACCESS_KEY=your_base_user_secret_key
AWS_REGION=ap-northeast-1
ROLE_ARN=arn:aws:iam::YOUR_ACCOUNT:role/FaceLivenessRole
EXTERNAL_ID=face-liveness-external-id
PORT=3000
```

### 2. IAM Setup
- **Base User**: có quyền `sts:AssumeRole` 
- **Target Role**: có policies cho `rekognition:CreateFaceLivenessSession` và `rekognition:GetFaceLivenessSessionResults`

### 3. Flutter App Configuration
- STS Server URL: `http://localhost:3000` (có thể thay đổi trong code)
- Auto refresh credentials khi hết hạn
- UI hiển thị trạng thái credentials

## Ưu điểm của STS approach

1. **Bảo mật cao hơn**: Temporary credentials với thời gian hết hạn
2. **Không cần Cognito**: Giảm complexity và cost
3. **Flexible**: Có thể integrate với any authentication system
4. **Fine-grained permissions**: IAM role có thể configured chính xác theo needs

## Luồng hoạt động chi tiết

1. **User mở app** → UI hiển thị "Chưa lấy credentials"
2. **User nhấn "Bắt đầu Face Liveness"**:
   - App gọi STS server (`POST /api/credentials`)
   - STS server thực hiện `sts:AssumeRole`
   - Trả về temporary credentials (access key, secret, session token)
   - App lưu credentials vào state
3. **Tạo Liveness Session**:
   - App gọi AWS Lambda qua Amplify API
   - Lambda tạo Rekognition Face Liveness session
4. **Face Liveness Detection**:
   - App truyền STS credentials vào `FaceLivenessDetector`
   - Native SDK sử dụng credentials để kết nối Rekognition
   - User thực hiện face liveness check
5. **Lấy kết quả**:
   - App gọi Lambda getResults qua Amplify API
   - Hiển thị confidence score và audit image

## Troubleshooting

### STS Server không chạy
- Kiểm tra `npm start` trong `credentials-server/`
- Verify port 3000 không bị block

### Credentials không hợp lệ
- Check AWS credentials trong `.env`
- Verify IAM role ARN
- Check ExternalId matching

### Face Liveness lỗi
- Ensure region matching giữa STS credentials và Lambda functions
- Check camera permissions
- Verify session ID valid

## Monitoring & Debugging

App hiển thị realtime status:
- ✅ **STS Credentials hợp lệ**: Region, expiration time
- ⚠️ **Credentials hết hạn**: Sẽ auto refresh
- ❌ **Chưa có credentials**: Cần lấy từ server

Console logs show:
- STS credential fetch process
- Credential expiration checks
- Face Liveness native SDK interactions 
# ⚡ Quick Start Guide

## Bước 1: Tạo AWS Credentials (nếu chưa có)

Cần AWS account và IAM user với permissions tạo role.

## Bước 2: <PERSON><PERSON><PERSON> hình cơ bản

```bash
# Cài đặt dependencies
npm install

# Copy và chỉnh sửa file environment
cp env.example .env
```

Chỉnh sửa `.env` với AWS credentials hiện tại của bạn:
```env
AWS_ACCESS_KEY_ID=your_existing_access_key
AWS_SECRET_ACCESS_KEY=your_existing_secret_key
AWS_REGION=us-east-1
```

## Bước 3: Lấy Account ID

```bash
npm run get-account
```

Copy ROLE_ARN từ output và paste vào `.env`:
```env
ROLE_ARN=arn:aws:iam::************:role/FaceLivenessRole
EXTERNAL_ID=face-liveness-external-id
```

## Bước 4: Tạo IAM Role trên AWS Console

1. **IAM → Roles → Create role**
2. **Custom trust policy**:

```json
{
  "Version": "2012-10-17", 
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {
        "AWS": "arn:aws:iam::YOUR_ACCOUNT_ID:user/YOUR_USER_NAME"
      },
      "Action": "sts:AssumeRole",
      "Condition": {
        "StringEquals": {
          "sts:ExternalId": "face-liveness-external-id"
        }
      }
    }
  ]
}
```

3. **Role name**: `FaceLivenessRole`
4. **Attach policies**: 
   - `AmazonRekognitionFullAccess` (hoặc custom policy)

## Bước 5: Test

```bash
# Start server
npm start

# Test trong terminal khác
curl -X POST http://localhost:3000/api/credentials \
  -H "Content-Type: application/json" \
  -d '{"sessionName": "test"}'
```

## ✅ Thành công nếu:

Response trả về credentials:
```json
{
  "success": true,
  "credentials": {
    "accessKeyId": "ASIA...",
    "secretAccessKey": "...",
    "sessionToken": "...",
    "expiration": "..."
  },
  "region": "us-east-1"
}
```

## 🆘 Troubleshooting

- **403 Forbidden**: Check trust policy ARN
- **Invalid role**: Check ROLE_ARN format  
- **External ID mismatch**: Check EXTERNAL_ID value

📖 **Chi tiết đầy đủ**: Xem `SETUP_GUIDE.md` 
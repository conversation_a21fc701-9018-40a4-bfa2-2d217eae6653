# 🔧 Hướng dẫn Setup STS AssumeRole cho Face Liveness

## Bước 1: Tạo IAM User (Base User)

1. **Đăng nhập AWS Console** → IAM → Users → Create User
2. **Tên user**: `face-liveness-base-user`
3. **Attach policies**: Chọn "Attach policies directly"
4. **Add policy**: `AmazonCognitoPowerUser` (tạm thời, sẽ thu hẹp sau)

### Lấy Access Keys:
1. Click vào user vừa tạo → Security credentials tab
2. Create access key → Command Line Interface (CLI)
3. **Lưu lại**: `Access Key ID` và `Secret Access Key`

## Bước 2: Tạo IAM Role (Target Role)

1. **IAM Console** → Roles → Create role
2. **Trusted entity type**: Custom trust policy
3. **Custom trust policy**:

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {
        "AWS": "arn:aws:iam::YOUR_ACCOUNT_ID:user/face-liveness-base-user"
      },
      "Action": "sts:AssumeRole",
      "Condition": {
        "StringEquals": {
          "sts:ExternalId": "face-liveness-external-id"
        }
      }
    }
  ]
}
```

4. **Role name**: `FaceLivenessRole`
5. **Description**: Role for Face Liveness operations

## Bước 3: Tạo Policy cho Face Liveness

1. **IAM Console** → Policies → Create policy
2. **JSON tab**:

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "rekognition:CreateFaceLivenessSession",
                "rekognition:GetFaceLivenessSessionResults"
            ],
            "Resource": "*"
        }
    ]
}
```

3. **Policy name**: `FaceLivenessPolicy`
4. **Attach policy** vào role `FaceLivenessRole`

## Bước 4: Thu hẹp quyền Base User

1. **Detach** `AmazonCognitoPowerUser` từ base user
2. **Tạo policy mới** cho base user:

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": "sts:AssumeRole",
            "Resource": "arn:aws:iam::YOUR_ACCOUNT_ID:role/FaceLivenessRole"
        }
    ]
}
```

3. **Policy name**: `AssumeRolePolicy`
4. **Attach** vào base user

## Bước 5: Cấu hình Environment Variables

Copy file `env.example` thành `.env`:

```bash
cp env.example .env
```

Chỉnh sửa file `.env`:

```env
# AWS Configuration (Base User credentials)
AWS_ACCESS_KEY_ID=AKIA... # Access key của base user
AWS_SECRET_ACCESS_KEY=... # Secret key của base user
AWS_REGION=us-east-1 # Hoặc region bạn muốn

# STS Role ARN (Target Role)
ROLE_ARN=arn:aws:iam::YOUR_ACCOUNT_ID:role/FaceLivenessRole

# Server Configuration
PORT=3000
```

## Bước 6: Test Setup

1. **Khởi động server**:
```bash
npm start
```

2. **Test health check**:
```bash
curl http://localhost:3000/health
```

3. **Test STS AssumeRole**:
```bash
curl -X POST http://localhost:3000/api/credentials \
  -H "Content-Type: application/json" \
  -d '{"sessionName": "test-session"}'
```

## Bước 7: Troubleshooting

### Lỗi: "User is not authorized to perform: sts:AssumeRole"
- Kiểm tra trust policy của role
- Đảm bảo base user có quyền assume role

### Lỗi: "Access Denied"
- Kiểm tra policy attach vào role
- Kiểm tra region settings

### Lỗi: "Invalid parameter"
- Kiểm tra format của ROLE_ARN
- Đảm bảo account ID đúng

## 📋 Checklist

- [ ] Tạo base user với access keys
- [ ] Tạo target role với trust policy
- [ ] Tạo và attach Face Liveness policy
- [ ] Thu hẹp quyền base user
- [ ] Cấu hình `.env` file
- [ ] Test thành công

## 🔐 Security Best Practices

1. **Rotate keys** định kỳ
2. **Sử dụng ExternalId** trong trust policy
3. **Thu hẹp permissions** tối đa
4. **Monitor CloudTrail** logs
5. **Expire sessions** nhanh (1 hour max)

## 📞 Support

Nếu gặp vấn đề, check:
1. CloudTrail logs để xem lỗi chi tiết
2. IAM Policy Simulator để test permissions
3. AWS STS decode-authorization-message để debug 
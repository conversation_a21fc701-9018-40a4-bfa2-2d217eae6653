const AWS = require('aws-sdk');
require('dotenv').config();

// Configure AWS
AWS.config.update({
  accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  region: process.env.AWS_REGION || 'us-east-1'
});

const sts = new AWS.STS();

async function getAccountId() {
  try {
    console.log('🔍 Getting AWS Account ID...');
    
    const result = await sts.getCallerIdentity().promise();
    
    console.log('✅ Account Information:');
    console.log(`   Account ID: ${result.Account}`);
    console.log(`   User ARN: ${result.Arn}`);
    console.log(`   User ID: ${result.UserId}`);
    console.log('');
    console.log('📋 Use this for your ROLE_ARN:');
    console.log(`   arn:aws:iam::${result.Account}:role/FaceLivenessRole`);
    
  } catch (error) {
    console.error('❌ Error getting account info:', error.message);
    console.log('');
    console.log('💡 Make sure your AWS credentials are configured correctly in .env file');
  }
}

getAccountId(); 
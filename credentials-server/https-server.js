const express = require('express');
const cors = require('cors');
const AWS = require('aws-sdk');
const https = require('https');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

const app = express();
const port = process.env.PORT || 3443;

// Middleware
app.use(cors());
app.use(express.json());

// AWS Configuration
AWS.config.update({
  accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  region: process.env.AWS_REGION || 'us-east-1'
});

const sts = new AWS.STS();

// Endpoint để lấy credentials sử dụng STS assumeRole
app.post('/api/credentials', async (req, res) => {
  try {
    const { sessionName = `face-liveness-${Date.now()}`, duration = 3600 } = req.body;
    
    const params = {
      RoleArn: process.env.ROLE_ARN,
      RoleSessionName: sessionName,
      DurationSeconds: duration
    };

    if (process.env.EXTERNAL_ID) {
      params.ExternalId = process.env.EXTERNAL_ID;
    }

    console.log('Assuming role with params:', { 
      ...params, 
      RoleArn: params.RoleArn ? params.RoleArn.slice(0, 20) + '...' : 'undefined',
      ExternalId: params.ExternalId ? '***' : 'not set'
    });
    
    const result = await sts.assumeRole(params).promise();
    
    const credentials = {
      accessKeyId: result.Credentials.AccessKeyId,
      secretAccessKey: result.Credentials.SecretAccessKey,
      sessionToken: result.Credentials.SessionToken,
      expiration: result.Credentials.Expiration
    };

    console.log('Successfully assumed role');
    
    res.json({
      success: true,
      credentials: credentials,
      region: process.env.AWS_REGION || 'us-east-1'
    });
  } catch (error) {
    console.error('Error assuming role:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      code: error.code
    });
  }
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

// Create self-signed certificate
const certPath = path.join(__dirname, 'server.crt');
const keyPath = path.join(__dirname, 'server.key');

// Check if certificates exist, if not create them
if (!fs.existsSync(certPath) || !fs.existsSync(keyPath)) {
  console.log('⚠️  SSL certificates not found. Creating self-signed certificates...');
  const { execSync } = require('child_process');
  
  try {
    execSync(`openssl req -x509 -newkey rsa:4096 -keyout ${keyPath} -out ${certPath} -days 365 -nodes -subj "/C=US/ST=Test/L=Test/O=Test/CN=localhost"`);
    console.log('✅ Self-signed certificates created');
  } catch (error) {
    console.error('❌ Failed to create certificates:', error.message);
    console.log('💡 Please install OpenSSL or use HTTP version');
    process.exit(1);
  }
}

const options = {
  key: fs.readFileSync(keyPath),
  cert: fs.readFileSync(certPath)
};

https.createServer(options, app).listen(port, '0.0.0.0', () => {
  console.log(`🚀 HTTPS STS AssumeRole Server running on:`);
  console.log(`   • https://localhost:${port} (local)`);
  console.log(`   • https://0.0.0.0:${port} (all interfaces)`);
  console.log(`   • Available on network for mobile devices (HTTPS)`);
  console.log('📋 Available endpoints:');
  console.log('   POST /api/credentials - Get STS AssumeRole credentials');
  console.log('   GET  /health - Health check');
  console.log('⚙️  Required environment variables:');
  console.log('   AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY, AWS_REGION, ROLE_ARN');
  console.log('⚠️  Note: Using self-signed certificate - iOS may show security warning');
}); 
{"version": "1.0", "examples": {"CreateChannel": [{"input": {"ChannelGroupName": "exampleChannelGroup", "ChannelName": "exampleChannel", "Description": "Description for exampleChannel", "InputType": "HLS", "Tags": {"key1": "value1", "key2": "value2"}}, "output": {"Arn": "arn:aws:mediapackagev2:us-west-2:123456789012:channelGroup/exampleChannelGroup/channel/exampleChannel", "ChannelGroupName": "exampleChannelGroup", "ChannelName": "exampleChannel", "CreatedAt": "2022-10-18T09:36:00.00Z", "Description": "Description for exampleChannel", "ETag": "GlfT+dwAyGIR4wuy8nKWl1RDPwSrjQej9qUutLZxoxk=", "IngestEndpoints": [{"Id": "1", "Url": "https://abcde-1.ingest.vwxyz.mediapackagev2.us-west-2.amazonaws.com/v1/exampleChannelGroup/exampleChannel/index"}, {"Id": "2", "Url": "https://abcde-2.ingest.vwxyz.mediapackagev2.us-west-2.amazonaws.com/v1/exampleChannelGroup/exampleChannel/index"}], "InputType": "HLS", "ModifiedAt": "2022-10-18T09:36:00.00Z", "Tags": {"key1": "value1", "key2": "value2"}}, "id": "example-1", "title": "Creating a Channel"}], "CreateChannelGroup": [{"input": {"ChannelGroupName": "exampleChannelGroup", "Description": "Description for exampleChannelGroup", "Tags": {"key1": "value1", "key2": "value2"}}, "output": {"Arn": "arn:aws:mediapackagev2:us-west-2:123456789012:channelGroup/exampleChannelGroup", "ChannelGroupName": "exampleChannelGroup", "CreatedAt": "2022-10-18T09:36:00.00Z", "Description": "Description for exampleChannelGroup", "ETag": "GlfT+dwAyGIR4wuy8nKWl1RDPwSrjQej9qUutLZxoxk=", "EgressDomain": "abcde.egress.vwxyz.mediapackagev2.us-west-2.amazonaws.com", "ModifiedAt": "2022-10-18T09:36:00.00Z", "Tags": {"key1": "value1", "key2": "value2"}}, "id": "example-1", "title": "Creating a Channel Group"}], "CreateOriginEndpoint": [{"input": {"ChannelGroupName": "exampleChannelGroup", "ChannelName": "exampleChannel", "ContainerType": "TS", "Description": "Description for exampleOriginEndpointTS", "ForceEndpointErrorConfiguration": {"EndpointErrorConditions": ["STALE_MANIFEST", "INCOMPLETE_MANIFEST", "MISSING_DRM_KEY", "SLATE_INPUT"]}, "HlsManifests": [{"ChildManifestName": "exampleChildManifest1", "ManifestName": "exampleManifest1", "ManifestWindowSeconds": 30, "ProgramDateTimeIntervalSeconds": 60, "ScteHls": {"AdMarkerHls": "DATERANGE"}}, {"ChildManifestName": "exampleManifest2", "ManifestName": "exampleManifest2", "ManifestWindowSeconds": 30, "ProgramDateTimeIntervalSeconds": 60, "ScteHls": {"AdMarkerHls": "DATERANGE"}}], "LowLatencyHlsManifests": [{"ChildManifestName": "exampleLLChildManifest1", "ManifestName": "exampleLLManifest1", "ManifestWindowSeconds": 30, "ProgramDateTimeIntervalSeconds": 60, "ScteHls": {"AdMarkerHls": "DATERANGE"}}, {"ChildManifestName": "exampleLLManifest2", "ManifestName": "exampleLLManifest2", "ManifestWindowSeconds": 30, "ProgramDateTimeIntervalSeconds": 60, "ScteHls": {"AdMarkerHls": "DATERANGE"}}], "OriginEndpointName": "exampleOriginEndpointTS", "Segment": {"Encryption": {"ConstantInitializationVector": "A382A901F3C1F7718512266CFFBB0B7E", "EncryptionMethod": {"TsEncryptionMethod": "AES_128"}, "KeyRotationIntervalSeconds": 300, "SpekeKeyProvider": {"DrmSystems": ["CLEAR_KEY_AES_128"], "EncryptionContractConfiguration": {"PresetSpeke20Audio": "SHARED", "PresetSpeke20Video": "SHARED"}, "ResourceId": "ResourceId", "RoleArn": "arn:aws:iam::123456789012:role/empRole", "Url": "https://foo.com"}}, "IncludeIframeOnlyStreams": true, "Scte": {"ScteFilter": ["SPLICE_INSERT", "BREAK"]}, "SegmentDurationSeconds": 6, "SegmentName": "segmentName", "TsIncludeDvbSubtitles": true, "TsUseAudioRenditionGroup": true}, "StartoverWindowSeconds": 300, "Tags": {"key1": "value1", "key2": "value2"}}, "output": {"Arn": "arn:aws:mediapackagev2:us-west-2:123456789012:channelGroup/exampleChannelGroup/channel/exampleChannel/originEndpoint/exampleOriginEndpointTS", "ChannelGroupName": "exampleChannelGroup", "ChannelName": "exampleChannel", "ContainerType": "TS", "CreatedAt": "2022-10-18T09:36:00.00Z", "Description": "Description for exampleOriginEndpointTS", "ETag": "GlfT+dwAyGIR4wuy8nKWl1RDPwSrjQej9qUutLZxoxk=", "ForceEndpointErrorConfiguration": {"EndpointErrorConditions": ["STALE_MANIFEST", "INCOMPLETE_MANIFEST", "MISSING_DRM_KEY", "SLATE_INPUT"]}, "HlsManifests": [{"ChildManifestName": "exampleChildManifest1", "ManifestName": "exampleManifest1", "ManifestWindowSeconds": 30, "ProgramDateTimeIntervalSeconds": 60, "ScteHls": {"AdMarkerHls": "DATERANGE"}, "Url": "https://abcde.egress.vwxyz.mediapackagev2.us-west-2.amazonaws.com/out/v1/exampleChannelGroup/exampleChannel/exampleOriginEndpointTS/exampleManifest1.m3u8"}, {"ChildManifestName": "exampleManifest2", "ManifestName": "exampleManifest2", "ManifestWindowSeconds": 30, "ProgramDateTimeIntervalSeconds": 60, "ScteHls": {"AdMarkerHls": "DATERANGE"}, "Url": "https://abcde.egress.vwxyz.mediapackagev2.us-west-2.amazonaws.com/out/v1/exampleChannelGroup/exampleChannel/exampleOriginEndpointTS/exampleManifest2.m3u8"}], "LowLatencyHlsManifests": [{"ChildManifestName": "exampleLLChildManifest1", "ManifestName": "exampleLLManifest1", "ManifestWindowSeconds": 30, "ProgramDateTimeIntervalSeconds": 60, "ScteHls": {"AdMarkerHls": "DATERANGE"}, "Url": "https://abcde.egress.vwxyz.mediapackagev2.us-west-2.amazonaws.com/out/v1/exampleChannelGroup/exampleChannel/exampleOriginEndpointTS/exampleLLManifest1.m3u8"}, {"ChildManifestName": "exampleLLManifest2", "ManifestName": "exampleLLManifest2", "ManifestWindowSeconds": 30, "ProgramDateTimeIntervalSeconds": 60, "ScteHls": {"AdMarkerHls": "DATERANGE"}, "Url": "https://abcde.egress.vwxyz.mediapackagev2.us-west-2.amazonaws.com/out/v1/exampleChannelGroup/exampleChannel/exampleOriginEndpointTS/exampleLLManifest2.m3u8"}], "ModifiedAt": "2022-10-18T09:36:00.00Z", "OriginEndpointName": "exampleOriginEndpointTS", "Segment": {"Encryption": {"ConstantInitializationVector": "A382A901F3C1F7718512266CFFBB0B7E", "EncryptionMethod": {"TsEncryptionMethod": "AES_128"}, "KeyRotationIntervalSeconds": 300, "SpekeKeyProvider": {"DrmSystems": ["CLEAR_KEY_AES_128"], "EncryptionContractConfiguration": {"PresetSpeke20Audio": "SHARED", "PresetSpeke20Video": "SHARED"}, "ResourceId": "ResourceId", "RoleArn": "arn:aws:iam::123456789012:role/empRole", "Url": "https://foo.com"}}, "IncludeIframeOnlyStreams": true, "Scte": {"ScteFilter": ["SPLICE_INSERT", "BREAK"]}, "SegmentDurationSeconds": 6, "SegmentName": "segmentName", "TsIncludeDvbSubtitles": true, "TsUseAudioRenditionGroup": true}, "StartoverWindowSeconds": 300, "Tags": {"key1": "value1", "key2": "value2"}}, "id": "example-1", "title": "Creating an OriginEndpoint with container type TS, and encryption enabled"}, {"input": {"ChannelGroupName": "exampleChannelGroup", "ChannelName": "exampleChannel", "ContainerType": "CMAF", "DashManifests": [{"DrmSignaling": "INDIVIDUAL", "ManifestName": "exampleDashManifest1", "ManifestWindowSeconds": 300, "MinBufferTimeSeconds": 30, "MinUpdatePeriodSeconds": 5, "PeriodTriggers": ["AVAILS"], "ScteDash": {"AdMarkerDash": "XML"}, "SegmentTemplateFormat": "NUMBER_WITH_TIMELINE", "SuggestedPresentationDelaySeconds": 2}, {"DrmSignaling": "INDIVIDUAL", "ManifestName": "exampleDashManifest2", "ManifestWindowSeconds": 60, "MinBufferTimeSeconds": 9, "MinUpdatePeriodSeconds": 3, "PeriodTriggers": ["AVAILS", "DRM_KEY_ROTATION", "SOURCE_CHANGES", "SOURCE_DISRUPTIONS"], "ScteDash": {"AdMarkerDash": "XML"}, "SegmentTemplateFormat": "NUMBER_WITH_TIMELINE", "SuggestedPresentationDelaySeconds": 12}], "ForceEndpointErrorConfiguration": {"EndpointErrorConditions": ["STALE_MANIFEST", "INCOMPLETE_MANIFEST", "MISSING_DRM_KEY", "SLATE_INPUT"]}, "HlsManifests": [{"ChildManifestName": "exampleChildManifest1", "ManifestName": "exampleManifest1", "ManifestWindowSeconds": 30, "ProgramDateTimeIntervalSeconds": 60, "ScteHls": {"AdMarkerHls": "DATERANGE"}}, {"ChildManifestName": "exampleManifest2", "ManifestName": "exampleManifest2", "ManifestWindowSeconds": 30, "ProgramDateTimeIntervalSeconds": 60, "ScteHls": {"AdMarkerHls": "DATERANGE"}}], "LowLatencyHlsManifests": [{"ChildManifestName": "exampleLLChildManifest1", "ManifestName": "exampleLLManifest1", "ManifestWindowSeconds": 30, "ProgramDateTimeIntervalSeconds": 60, "ScteHls": {"AdMarkerHls": "DATERANGE"}}, {"ChildManifestName": "exampleLLManifest2", "ManifestName": "exampleLLManifest2", "ManifestWindowSeconds": 30, "ProgramDateTimeIntervalSeconds": 60, "ScteHls": {"AdMarkerHls": "DATERANGE"}}], "OriginEndpointName": "exampleOriginEndpointCMAF", "Segment": {"Encryption": {"ConstantInitializationVector": "A382A901F3C1F7718512266CFFBB0B9F", "EncryptionMethod": {"CmafEncryptionMethod": "CBCS"}, "KeyRotationIntervalSeconds": 300, "SpekeKeyProvider": {"DrmSystems": ["PLAYREADY", "WIDEVINE"], "EncryptionContractConfiguration": {"PresetSpeke20Audio": "PRESET_AUDIO_1", "PresetSpeke20Video": "PRESET_VIDEO_1"}, "ResourceId": "ResourceId", "RoleArn": "arn:aws:iam::123456789012:role/empRole", "Url": "https://foo.com"}}, "IncludeIframeOnlyStreams": true, "Scte": {"ScteFilter": ["SPLICE_INSERT", "BREAK"]}, "SegmentDurationSeconds": 6, "SegmentName": "segmentName"}, "StartoverWindowSeconds": 300, "Tags": {"key1": "value1", "key2": "value2"}}, "output": {"Arn": "arn:aws:mediapackagev2:us-west-2:123456789012:channelGroup/exampleChannelGroup/channel/exampleChannel/originEndpoint/exampleOriginEndpointCMAF", "ChannelGroupName": "exampleChannelGroup", "ChannelName": "exampleChannel", "ContainerType": "CMAF", "CreatedAt": "2022-10-18T09:36:00.00Z", "DashManifests": [{"DrmSignaling": "INDIVIDUAL", "ManifestName": "exampleDashManifest1", "ManifestWindowSeconds": 300, "MinBufferTimeSeconds": 30, "MinUpdatePeriodSeconds": 5, "PeriodTriggers": ["AVAILS"], "ScteDash": {"AdMarkerDash": "XML"}, "SegmentTemplateFormat": "NUMBER_WITH_TIMELINE", "SuggestedPresentationDelaySeconds": 2, "Url": "https://abcde.egress.vwxyz.mediapackagev2.us-west-2.amazonaws.com/out/v1/exampleChannelGroup/exampleChannel/exampleOriginEndpointCMAF/exampleDashManifest1.mpd"}, {"DrmSignaling": "INDIVIDUAL", "ManifestName": "exampleDashManifest2", "ManifestWindowSeconds": 60, "MinBufferTimeSeconds": 9, "MinUpdatePeriodSeconds": 3, "PeriodTriggers": ["AVAILS", "DRM_KEY_ROTATION", "SOURCE_CHANGES", "SOURCE_DISRUPTIONS"], "ScteDash": {"AdMarkerDash": "XML"}, "SegmentTemplateFormat": "NUMBER_WITH_TIMELINE", "SuggestedPresentationDelaySeconds": 12, "Url": "https://abcde.egress.vwxyz.mediapackagev2.us-west-2.amazonaws.com/out/v1/exampleChannelGroup/exampleChannel/exampleOriginEndpointCMAF/exampleDashManifest2.mpd"}], "ETag": "GlfT+dwAyGIR4wuy8nKWl1RDPwSrjQej9qUutLZxoxk=", "ForceEndpointErrorConfiguration": {"EndpointErrorConditions": ["STALE_MANIFEST", "INCOMPLETE_MANIFEST", "MISSING_DRM_KEY", "SLATE_INPUT"]}, "HlsManifests": [{"ChildManifestName": "exampleChildManifest1", "ManifestName": "exampleManifest1", "ManifestWindowSeconds": 30, "ProgramDateTimeIntervalSeconds": 60, "ScteHls": {"AdMarkerHls": "DATERANGE"}, "Url": "https://abcde.egress.vwxyz.mediapackagev2.us-west-2.amazonaws.com/out/v1/exampleChannelGroup/exampleChannel/exampleOriginEndpointCMAF/exampleManifest1.m3u8"}, {"ChildManifestName": "exampleManifest2", "ManifestName": "exampleManifest2", "ManifestWindowSeconds": 30, "ProgramDateTimeIntervalSeconds": 60, "ScteHls": {"AdMarkerHls": "DATERANGE"}, "Url": "https://abcde.egress.vwxyz.mediapackagev2.us-west-2.amazonaws.com/out/v1/exampleChannelGroup/exampleChannel/exampleOriginEndpointCMAF/exampleManifest2.m3u8"}], "LowLatencyHlsManifests": [{"ChildManifestName": "exampleLLChildManifest1", "ManifestName": "exampleLLManifest1", "ManifestWindowSeconds": 30, "ProgramDateTimeIntervalSeconds": 60, "ScteHls": {"AdMarkerHls": "DATERANGE"}, "Url": "https://abcde.egress.vwxyz.mediapackagev2.us-west-2.amazonaws.com/out/v1/exampleChannelGroup/exampleChannel/exampleOriginEndpointCMAF/exampleLLManifest1.m3u8"}, {"ChildManifestName": "exampleLLManifest2", "ManifestName": "exampleLLManifest2", "ManifestWindowSeconds": 30, "ProgramDateTimeIntervalSeconds": 60, "ScteHls": {"AdMarkerHls": "DATERANGE"}, "Url": "https://abcde.egress.vwxyz.mediapackagev2.us-west-2.amazonaws.com/out/v1/exampleChannelGroup/exampleChannel/exampleOriginEndpointCMAF/exampleLLManifest2.m3u8"}], "ModifiedAt": "2022-10-18T09:36:00.00Z", "OriginEndpointName": "exampleOriginEndpointCMAF", "Segment": {"Encryption": {"ConstantInitializationVector": "A382A901F3C1F7718512266CFFBB0B9F", "EncryptionMethod": {"CmafEncryptionMethod": "CBCS"}, "KeyRotationIntervalSeconds": 300, "SpekeKeyProvider": {"DrmSystems": ["PLAYREADY", "WIDEVINE"], "EncryptionContractConfiguration": {"PresetSpeke20Audio": "PRESET_AUDIO_1", "PresetSpeke20Video": "PRESET_VIDEO_1"}, "ResourceId": "ResourceId", "RoleArn": "arn:aws:iam::123456789012:role/empRole", "Url": "https://foo.com"}}, "IncludeIframeOnlyStreams": true, "Scte": {"ScteFilter": ["SPLICE_INSERT", "BREAK"]}, "SegmentDurationSeconds": 6, "SegmentName": "segmentName"}, "StartoverWindowSeconds": 300, "Tags": {"key1": "value1", "key2": "value2"}}, "id": "example-2", "title": "Creating an OriginEndpoint with container type CMAF, and encryption enabled"}], "DeleteChannel": [{"input": {"ChannelGroupName": "exampleChannelGroup", "ChannelName": "exampleChannel"}, "output": {}, "id": "example-1", "title": "Deleting a Channel"}], "DeleteChannelGroup": [{"input": {"ChannelGroupName": "exampleChannelGroup"}, "output": {}, "id": "example-1", "title": "Deleting a Channel Group"}], "DeleteChannelPolicy": [{"input": {"ChannelGroupName": "exampleChannelGroup", "ChannelName": "exampleChannel"}, "output": {}, "id": "example-1", "title": "Deleting a Channel Policy"}], "DeleteOriginEndpoint": [{"input": {"ChannelGroupName": "exampleChannelGroup", "ChannelName": "exampleChannel", "OriginEndpointName": "exampleOriginEndpointTS"}, "output": {}, "id": "example-1", "title": "Deleting an OriginEndpoint"}], "DeleteOriginEndpointPolicy": [{"input": {"ChannelGroupName": "exampleChannelGroup", "ChannelName": "exampleChannel", "OriginEndpointName": "exampleOriginEndpoint"}, "output": {}, "id": "example-1", "title": "Deleting an Origin Endpoint Policy"}], "GetChannel": [{"input": {"ChannelGroupName": "exampleChannelGroup", "ChannelName": "exampleChannel"}, "output": {"Arn": "arn:aws:mediapackagev2:us-west-2:123456789012:channelGroup/exampleChannelGroup/channel/exampleChannel", "ChannelGroupName": "exampleChannelGroup", "ChannelName": "exampleChannel", "CreatedAt": "2022-10-18T09:36:00.00Z", "Description": "Description for exampleChannel", "ETag": "GlfT+dwAyGIR4wuy8nKWl1RDPwSrjQej9qUutLZxoxk=", "IngestEndpoints": [{"Id": "1", "Url": "https://abcde-1.ingest.vwxyz.mediapackagev2.us-west-2.amazonaws.com/v1/exampleChannelGroup/exampleChannel/index"}, {"Id": "2", "Url": "https://abcde-2.ingest.vwxyz.mediapackagev2.us-west-2.amazonaws.com/v1/exampleChannelGroup/exampleChannel/index"}], "InputType": "HLS", "ModifiedAt": "2022-10-18T09:36:00.00Z", "Tags": {"key1": "value1", "key2": "value2"}}, "id": "example-1", "title": "Getting a Channel"}], "GetChannelGroup": [{"input": {"ChannelGroupName": "exampleChannelGroup"}, "output": {"Arn": "arn:aws:mediapackagev2:us-west-2:123456789012:channelGroup/exampleChannelGroup", "ChannelGroupName": "exampleChannelGroup", "CreatedAt": "2022-10-18T09:36:00.00Z", "Description": "Description for exampleChannelGroup", "ETag": "GlfT+dwAyGIR4wuy8nKWl1RDPwSrjQej9qUutLZxoxk=", "EgressDomain": "abcde.egress.vwxyz.mediapackagev2.us-west-2.amazonaws.com", "ModifiedAt": "2022-10-18T09:36:00.00Z", "Tags": {"key1": "value1", "key2": "value2"}}, "id": "example-1", "title": "Getting a Channel Group"}], "GetChannelPolicy": [{"input": {"ChannelGroupName": "exampleChannelGroup", "ChannelName": "exampleChannel"}, "output": {"ChannelGroupName": "exampleChannelGroup", "ChannelName": "exampleChannel", "Policy": "{...}"}, "id": "example-1", "title": "Getting a Channel Policy"}], "GetOriginEndpoint": [{"input": {"ChannelGroupName": "exampleChannelGroup", "ChannelName": "exampleChannel", "OriginEndpointName": "exampleOriginEndpointTS"}, "output": {"Arn": "arn:aws:mediapackagev2:us-west-2:123456789012:channelGroup/exampleChannelGroup/channel/exampleChannel/originEndpoint/exampleOriginEndpointTS", "ChannelGroupName": "exampleChannelGroup", "ChannelName": "exampleChannel", "ContainerType": "TS", "CreatedAt": "2022-10-18T09:36:00.00Z", "Description": "Description for exampleOriginEndpointTS", "ETag": "GlfT+dwAyGIR4wuy8nKWl1RDPwSrjQej9qUutLZxoxk=", "ForceEndpointErrorConfiguration": {"EndpointErrorConditions": ["STALE_MANIFEST", "INCOMPLETE_MANIFEST", "MISSING_DRM_KEY", "SLATE_INPUT"]}, "HlsManifests": [{"ChildManifestName": "exampleChildManifest1", "ManifestName": "exampleManifest1", "ManifestWindowSeconds": 30, "ProgramDateTimeIntervalSeconds": 60, "ScteHls": {"AdMarkerHls": "DATERANGE"}, "Url": "https://abcde.egress.vwxyz.mediapackagev2.us-west-2.amazonaws.com/out/v1/exampleChannelGroup/exampleChannel/exampleOriginEndpointTS/exampleManifest1.m3u8"}, {"ChildManifestName": "exampleManifest2", "ManifestName": "exampleManifest2", "ManifestWindowSeconds": 30, "ProgramDateTimeIntervalSeconds": 60, "ScteHls": {"AdMarkerHls": "DATERANGE"}, "Url": "https://abcde.egress.vwxyz.mediapackagev2.us-west-2.amazonaws.com/out/v1/exampleChannelGroup/exampleChannel/exampleOriginEndpointTS/exampleManifest2.m3u8"}], "LowLatencyHlsManifests": [{"ChildManifestName": "exampleLLChildManifest1", "ManifestName": "exampleLLManifest1", "ManifestWindowSeconds": 30, "ProgramDateTimeIntervalSeconds": 60, "ScteHls": {"AdMarkerHls": "DATERANGE"}, "Url": "https://abcde.egress.vwxyz.mediapackagev2.us-west-2.amazonaws.com/out/v1/exampleChannelGroup/exampleChannel/exampleOriginEndpointTS/exampleLLManifest1.m3u8"}, {"ChildManifestName": "exampleLLManifest2", "ManifestName": "exampleLLManifest2", "ManifestWindowSeconds": 30, "ProgramDateTimeIntervalSeconds": 60, "ScteHls": {"AdMarkerHls": "DATERANGE"}, "Url": "https://abcde.egress.vwxyz.mediapackagev2.us-west-2.amazonaws.com/out/v1/exampleChannelGroup/exampleChannel/exampleOriginEndpointTS/exampleLLManifest2.m3u8"}], "ModifiedAt": "2022-10-18T09:36:00.00Z", "OriginEndpointName": "exampleOriginEndpointTS", "Segment": {"Encryption": {"ConstantInitializationVector": "A382A901F3C1F7718512266CFFBB0B7E", "EncryptionMethod": {"TsEncryptionMethod": "AES_128"}, "KeyRotationIntervalSeconds": 300, "SpekeKeyProvider": {"DrmSystems": ["CLEAR_KEY_AES_128"], "EncryptionContractConfiguration": {"PresetSpeke20Audio": "SHARED", "PresetSpeke20Video": "SHARED"}, "ResourceId": "ResourceId", "RoleArn": "arn:aws:iam::123456789012:role/empRole", "Url": "https://foo.com"}}, "IncludeIframeOnlyStreams": true, "Scte": {"ScteFilter": ["SPLICE_INSERT", "BREAK"]}, "SegmentDurationSeconds": 6, "SegmentName": "segmentName", "TsIncludeDvbSubtitles": true, "TsUseAudioRenditionGroup": true}, "StartoverWindowSeconds": 300, "Tags": {"key1": "value1", "key2": "value2"}}, "id": "example-1", "title": "Getting an OriginEndpoint"}], "GetOriginEndpointPolicy": [{"input": {"ChannelGroupName": "exampleChannelGroup", "ChannelName": "exampleChannel", "OriginEndpointName": "exampleOriginEndpoint"}, "output": {"ChannelGroupName": "exampleChannelGroup", "ChannelName": "exampleChannel", "OriginEndpointName": "exampleOriginEndpoint", "Policy": "{...}"}, "id": "example-1", "title": "Getting an Origin Endpoint Policy"}], "ListChannelGroups": [{"input": {}, "output": {"Items": [{"Arn": "arn:aws:mediapackagev2:us-west-2:123456789012:channelGroup/exampleChannelGroup", "ChannelGroupName": "exampleChannelGroup", "CreatedAt": "2022-10-18T09:36:00.00Z", "Description": "Description for exampleChannelGroup", "ModifiedAt": "2022-10-18T09:36:00.00Z"}, {"Arn": "arn:aws:mediapackagev2:us-west-2:123456789012:channelGroup/anotherExampleChannelGroup", "ChannelGroupName": "anotherExampleChannelGroup", "CreatedAt": "2022-10-18T10:36:00.00Z", "ModifiedAt": "2022-10-18T10:36:00.00Z"}]}, "id": "example-1", "title": "Listing all Channel Groups"}], "ListChannels": [{"input": {"ChannelGroupName": "exampleChannelGroup"}, "output": {"Items": [{"Arn": "arn:aws:mediapackagev2:us-west-2:123456789012:channelGroup/exampleChannelGroup/channel/exampleChannel", "ChannelGroupName": "exampleChannelGroup", "ChannelName": "exampleChannel", "CreatedAt": "2022-10-18T09:36:00.00Z", "Description": "Description for exampleChannel", "ModifiedAt": "2022-10-18T09:36:00.00Z"}, {"Arn": "arn:aws:mediapackagev2:us-west-2:123456789012:channelGroup/exampleChannelGroup/channel/anotherExampleChannel", "ChannelGroupName": "exampleChannelGroup", "ChannelName": "anotherExampleChannel", "CreatedAt": "2022-10-18T10:36:00.00Z", "ModifiedAt": "2022-10-18T10:36:00.00Z"}]}, "id": "example-1", "title": "Listing all Channels"}], "ListOriginEndpoints": [{"input": {"ChannelGroupName": "exampleChannelGroup", "ChannelName": "exampleChannel"}, "output": {"Items": [{"Arn": "arn:aws:mediapackagev2:us-west-2:123456789012:channelGroup/exampleChannelGroup/channel/exampleChannel/originEndpoint/exampleOriginEndpointTS", "ChannelGroupName": "exampleChannelGroup", "ChannelName": "exampleChannel", "ContainerType": "TS", "CreatedAt": "2022-10-18T09:36:00.00Z", "Description": "Description for exampleOriginEndpointTS", "ForceEndpointErrorConfiguration": {"EndpointErrorConditions": ["STALE_MANIFEST", "INCOMPLETE_MANIFEST", "MISSING_DRM_KEY", "SLATE_INPUT"]}, "HlsManifests": [{"ChildManifestName": "exampleChildManifest1", "ManifestName": "exampleManifest1", "Url": "https://abcde.egress.vwxyz.mediapackagev2.us-west-2.amazonaws.com/out/v1/exampleChannelGroup/exampleChannel/exampleOriginEndpointTS/exampleManifest1.m3u8"}, {"ChildManifestName": "exampleManifest2", "ManifestName": "exampleManifest2", "Url": "https://abcde.egress.vwxyz.mediapackagev2.us-west-2.amazonaws.com/out/v1/exampleChannelGroup/exampleChannel/exampleOriginEndpointTS/exampleManifest2.m3u8"}], "LowLatencyHlsManifests": [{"ChildManifestName": "exampleLLChildManifest1", "ManifestName": "exampleLLManifest1", "Url": "https://abcde.egress.vwxyz.mediapackagev2.us-west-2.amazonaws.com/out/v1/exampleChannelGroup/exampleChannel/exampleOriginEndpointTS/exampleLLManifest1.m3u8"}, {"ChildManifestName": "exampleLLManifest2", "ManifestName": "exampleLLManifest2", "Url": "https://abcde.egress.vwxyz.mediapackagev2.us-west-2.amazonaws.com/out/v1/exampleChannelGroup/exampleChannel/exampleOriginEndpointTS/exampleLLManifest2.m3u8"}], "ModifiedAt": "2022-10-18T09:36:00.00Z", "OriginEndpointName": "exampleOriginEndpointTS"}, {"Arn": "arn:aws:mediapackagev2:us-west-2:123456789012:channelGroup/exampleChannelGroup/channel/exampleChannel/originEndpoint/exampleOriginEndpointCMAF", "ChannelGroupName": "exampleChannelGroup", "ChannelName": "exampleChannel", "ContainerType": "CMAF", "CreatedAt": "2022-10-18T09:36:00.00Z", "DashManifests": [{"ManifestName": "exampleDashManifest1", "Url": "https://abcde.egress.vwxyz.mediapackagev2.us-west-2.amazonaws.com/out/v1/exampleChannelGroup/exampleChannel/exampleOriginEndpointCMAF/exampleDashManifest1.mpd"}, {"ManifestName": "exampleDashManifest2", "Url": "https://abcde.egress.vwxyz.mediapackagev2.us-west-2.amazonaws.com/out/v1/exampleChannelGroup/exampleChannel/exampleOriginEndpointCMAF/exampleDashManifest2.mpd"}], "ForceEndpointErrorConfiguration": {"EndpointErrorConditions": ["STALE_MANIFEST", "INCOMPLETE_MANIFEST", "MISSING_DRM_KEY", "SLATE_INPUT"]}, "HlsManifests": [{"ChildManifestName": "exampleChildManifest1", "ManifestName": "exampleManifest1", "Url": "https://abcde.egress.vwxyz.mediapackagev2.us-west-2.amazonaws.com/out/v1/exampleChannelGroup/exampleChannel/exampleOriginEndpointCMAF/exampleManifest1.m3u8"}, {"ChildManifestName": "exampleManifest2", "ManifestName": "exampleManifest2", "Url": "https://abcde.egress.vwxyz.mediapackagev2.us-west-2.amazonaws.com/out/v1/exampleChannelGroup/exampleChannel/exampleOriginEndpointCMAF/exampleManifest2.m3u8"}], "LowLatencyHlsManifests": [{"ChildManifestName": "exampleLLChildManifest1", "ManifestName": "exampleLLManifest1", "Url": "https://abcde.egress.vwxyz.mediapackagev2.us-west-2.amazonaws.com/out/v1/exampleChannelGroup/exampleChannel/exampleOriginEndpointCMAF/exampleLLManifest1.m3u8"}, {"ChildManifestName": "exampleLLManifest2", "ManifestName": "exampleLLManifest2", "Url": "https://abcde.egress.vwxyz.mediapackagev2.us-west-2.amazonaws.com/out/v1/exampleChannelGroup/exampleChannel/exampleOriginEndpointCMAF/exampleLLManifest2.m3u8"}], "ModifiedAt": "2022-10-18T09:36:00.00Z", "OriginEndpointName": "exampleOriginEndpointCMAF"}]}, "id": "example-1", "title": "Listing all OriginEndpoints"}], "ListTagsForResource": [{"input": {"ResourceArn": "arn:aws:mediapackagev2:us-west-2:123456789012:channelGroup/exampleChannelGroup/channel/exampleChannel"}, "output": {"Tags": {"key1": "value1", "key2": "value2"}}, "id": "example-1", "title": "List all tags for a resource"}], "PutChannelPolicy": [{"input": {"ChannelGroupName": "exampleChannelGroup", "ChannelName": "exampleChannel", "Policy": "{...}"}, "output": {}, "id": "example-1", "title": "Creating a Channel Policy"}], "PutOriginEndpointPolicy": [{"input": {"ChannelGroupName": "exampleChannelGroup", "ChannelName": "exampleChannel", "OriginEndpointName": "exampleOriginEndpoint", "Policy": "{...}"}, "output": {}, "id": "example-1", "title": "Creating an Origin Endpoint Policy"}], "TagResource": [{"input": {"ResourceArn": "arn:aws:mediapackagev2:us-west-2:123456789012:channelGroup/exampleChannelGroup/channel/exampleChannel", "Tags": {"key3": "value3", "key4": "value4"}}, "output": {}, "id": "example-1", "title": "Add tags to a resource"}], "UntagResource": [{"input": {"ResourceArn": "arn:aws:mediapackagev2:us-west-2:123456789012:channelGroup/exampleChannelGroup/channel/exampleChannel", "TagKeys": ["key3", "key4"]}, "output": {}, "id": "example-1", "title": "Remove tags from a resource"}], "UpdateChannel": [{"input": {"ChannelGroupName": "exampleChannelGroup", "ChannelName": "exampleChannel", "Description": "Updated description for exampleChannel"}, "output": {"Arn": "arn:aws:mediapackagev2:us-west-2:123456789012:channelGroup/exampleChannelGroup/channel/exampleChannel", "ChannelGroupName": "exampleChannelGroup", "ChannelName": "exampleChannel", "CreatedAt": "2022-10-18T09:36:00.00Z", "Description": "Updated description for exampleChannel", "ETag": "GlfT+dwAyGIR4wuy8nKWl1RDPwSrjQej9qUutLZxoxk=", "IngestEndpoints": [{"Id": "1", "Url": "https://abcde-1.ingest.vwxyz.mediapackagev2.us-west-2.amazonaws.com/v1/exampleChannelGroup/exampleChannel/index"}, {"Id": "2", "Url": "https://abcde-2.ingest.vwxyz.mediapackagev2.us-west-2.amazonaws.com/v1/exampleChannelGroup/exampleChannel/index"}], "InputType": "HLS", "ModifiedAt": "2022-10-18T10:36:00.00Z", "Tags": {"key1": "value1", "key2": "value2"}}, "id": "example-1", "title": "Updating a Channel"}], "UpdateChannelGroup": [{"input": {"ChannelGroupName": "exampleChannelGroup", "Description": "Updated description for exampleChannelGroup"}, "output": {"Arn": "arn:aws:mediapackagev2:us-west-2:123456789012:channelGroup/exampleChannelGroup", "ChannelGroupName": "exampleChannelGroup", "CreatedAt": "2022-10-18T09:36:00.00Z", "Description": "Updated description for exampleChannelGroup", "ETag": "GlfT+dwAyGIR4wuy8nKWl1RDPwSrjQej9qUutLZxoxk=", "EgressDomain": "abcde.egress.vwxyz.mediapackagev2.us-west-2.amazonaws.com", "ModifiedAt": "2022-10-18T10:36:00.00Z", "Tags": {"key1": "value1", "key2": "value2"}}, "id": "example-1", "title": "Updating a Channel Group"}], "UpdateOriginEndpoint": [{"input": {"ChannelGroupName": "exampleChannelGroup", "ChannelName": "exampleChannel", "ContainerType": "TS", "Description": "Updated description for exampleOriginEndpointTS", "ForceEndpointErrorConfiguration": {"EndpointErrorConditions": ["STALE_MANIFEST", "INCOMPLETE_MANIFEST", "MISSING_DRM_KEY", "SLATE_INPUT"]}, "HlsManifests": [{"ChildManifestName": "exampleChildManifest1", "ManifestName": "exampleManifest1", "ManifestWindowSeconds": 30, "ProgramDateTimeIntervalSeconds": 60, "ScteHls": {"AdMarkerHls": "DATERANGE"}}, {"ChildManifestName": "exampleManifest2", "ManifestName": "exampleManifest2", "ManifestWindowSeconds": 30, "ProgramDateTimeIntervalSeconds": 60, "ScteHls": {"AdMarkerHls": "DATERANGE"}}], "LowLatencyHlsManifests": [{"ChildManifestName": "exampleLLChildManifest1", "ManifestName": "exampleLLManifest1", "ManifestWindowSeconds": 30, "ProgramDateTimeIntervalSeconds": 60, "ScteHls": {"AdMarkerHls": "DATERANGE"}}, {"ChildManifestName": "exampleLLManifest2", "ManifestName": "exampleLLManifest2", "ManifestWindowSeconds": 30, "ProgramDateTimeIntervalSeconds": 60, "ScteHls": {"AdMarkerHls": "DATERANGE"}}], "OriginEndpointName": "exampleOriginEndpointTS", "Segment": {"Encryption": {"ConstantInitializationVector": "A382A901F3C1F7718512266CFFBB0B7E", "EncryptionMethod": {"TsEncryptionMethod": "AES_128"}, "KeyRotationIntervalSeconds": 300, "SpekeKeyProvider": {"DrmSystems": ["CLEAR_KEY_AES_128"], "EncryptionContractConfiguration": {"PresetSpeke20Audio": "SHARED", "PresetSpeke20Video": "SHARED"}, "ResourceId": "ResourceId", "RoleArn": "arn:aws:iam::123456789012:role/empRole", "Url": "https://foo.com"}}, "IncludeIframeOnlyStreams": false, "Scte": {"ScteFilter": ["SPLICE_INSERT"]}, "SegmentDurationSeconds": 7, "SegmentName": "segmentName2", "TsIncludeDvbSubtitles": false, "TsUseAudioRenditionGroup": true}, "StartoverWindowSeconds": 600}, "output": {"Arn": "arn:aws:mediapackagev2:us-west-2:123456789012:channelGroup/exampleChannelGroup/channel/exampleChannel/originEndpoint/exampleOriginEndpointTS", "ChannelGroupName": "exampleChannelGroup", "ChannelName": "exampleChannel", "ContainerType": "TS", "CreatedAt": "2022-10-18T09:36:00.00Z", "Description": "Updated description for exampleOriginEndpointTS", "ETag": "GlfT+dwAyGIR4wuy8nKWl1RDPwSrjQej9qUutLZxoxk=", "ForceEndpointErrorConfiguration": {"EndpointErrorConditions": ["STALE_MANIFEST", "INCOMPLETE_MANIFEST", "MISSING_DRM_KEY", "SLATE_INPUT"]}, "HlsManifests": [{"ChildManifestName": "exampleChildManifest1", "ManifestName": "exampleManifest1", "ManifestWindowSeconds": 30, "ProgramDateTimeIntervalSeconds": 60, "ScteHls": {"AdMarkerHls": "DATERANGE"}, "Url": "https://abcde.egress.vwxyz.mediapackagev2.us-west-2.amazonaws.com/out/v1/exampleChannelGroup/exampleChannel/exampleOriginEndpointTS/exampleManifest1.m3u8"}, {"ChildManifestName": "exampleManifest2", "ManifestName": "exampleManifest2", "ManifestWindowSeconds": 30, "ProgramDateTimeIntervalSeconds": 60, "ScteHls": {"AdMarkerHls": "DATERANGE"}, "Url": "https://abcde.egress.vwxyz.mediapackagev2.us-west-2.amazonaws.com/out/v1/exampleChannelGroup/exampleChannel/exampleOriginEndpointTS/exampleManifest2.m3u8"}], "LowLatencyHlsManifests": [{"ChildManifestName": "exampleLLChildManifest1", "ManifestName": "exampleLLManifest1", "ManifestWindowSeconds": 30, "ProgramDateTimeIntervalSeconds": 60, "ScteHls": {"AdMarkerHls": "DATERANGE"}, "Url": "https://abcde.egress.vwxyz.mediapackagev2.us-west-2.amazonaws.com/out/v1/exampleChannelGroup/exampleChannel/exampleOriginEndpointTS/exampleLLManifest1.m3u8"}, {"ChildManifestName": "exampleLLManifest2", "ManifestName": "exampleLLManifest2", "ManifestWindowSeconds": 30, "ProgramDateTimeIntervalSeconds": 60, "ScteHls": {"AdMarkerHls": "DATERANGE"}, "Url": "https://abcde.egress.vwxyz.mediapackagev2.us-west-2.amazonaws.com/out/v1/exampleChannelGroup/exampleChannel/exampleOriginEndpointTS/exampleLLManifest2.m3u8"}], "ModifiedAt": "2022-10-18T09:36:00.00Z", "OriginEndpointName": "exampleOriginEndpointTS", "Segment": {"Encryption": {"ConstantInitializationVector": "A382A901F3C1F7718512266CFFBB0B7E", "EncryptionMethod": {"TsEncryptionMethod": "AES_128"}, "KeyRotationIntervalSeconds": 300, "SpekeKeyProvider": {"DrmSystems": ["CLEAR_KEY_AES_128"], "EncryptionContractConfiguration": {"PresetSpeke20Audio": "SHARED", "PresetSpeke20Video": "SHARED"}, "ResourceId": "ResourceId", "RoleArn": "arn:aws:iam::123456789012:role/empRole", "Url": "https://foo.com"}}, "IncludeIframeOnlyStreams": false, "Scte": {"ScteFilter": ["SPLICE_INSERT"]}, "SegmentDurationSeconds": 7, "SegmentName": "segmentName2", "TsIncludeDvbSubtitles": false, "TsUseAudioRenditionGroup": true}, "StartoverWindowSeconds": 600, "Tags": {"key1": "value1", "key2": "value2"}}, "id": "example-1", "title": "Updating an OriginEndpoint"}]}}